<f:if condition="{routes}">
    <f:then>
        <div class="destinations-menu">
            <f:for each="{routes}" as="route">
                <a href="{route.routeSlug}" class="destination-link">
                    {route.originName} → {route.destinationName}
                </a>
            </f:for>
        </div>
    </f:then>
    <f:else>
        <div class="no-routes">
            {f:translate(key: 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:no_routes_available', default: 'No routes available')}
        </div>
    </f:else>
</f:if>
